const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const { query: dbQuery, transaction } = require('../database/connection');
const { authenticateToken } = require('../middleware/auth');
const { logActivity } = require('../middleware/activityLogger');
const { parseUserAgent } = require('../utils/userAgent');

const router = express.Router();

/**
 * Login endpoint
 * POST /api/auth/login
 */
router.post('/login', [
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { email, password } = req.body;
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent');
    const browserInfo = parseUserAgent(userAgent);

    // Find user
    const userResult = await dbQuery(
      'SELECT id, email, password_hash, role, is_active, preferred_language FROM users WHERE email = $1',
      [email]
    );

    if (userResult.rows.length === 0) {
      return res.status(401).json({
        error: 'Authentication failed',
        message: 'Invalid credentials'
      });
    }

    const user = userResult.rows[0];

    // Check if user is active
    if (!user.is_active) {
      return res.status(401).json({
        error: 'Account disabled',
        message: 'Your account has been disabled'
      });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({
        error: 'Authentication failed',
        message: 'Invalid credentials'
      });
    }

    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        email: user.email, 
        role: user.role 
      },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    // Create session and log activity in transaction
    await transaction(async (client) => {
      // Update last login
      await client.query(
        'UPDATE users SET last_login_at = CURRENT_TIMESTAMP WHERE id = $1',
        [user.id]
      );

      // Create session
      const sessionResult = await client.query(`
        INSERT INTO user_sessions (user_id, session_token, ip_address, user_agent, browser_info, expires_at)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id
      `, [
        user.id,
        token,
        ipAddress,
        userAgent,
        JSON.stringify(browserInfo),
        new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      ]);

      // Log login activity
      await client.query(`
        INSERT INTO user_activities (user_id, session_id, activity_type, activity_data, ip_address, user_agent)
        VALUES ($1, $2, $3, $4, $5, $6)
      `, [
        user.id,
        sessionResult.rows[0].id,
        'login',
        JSON.stringify({ success: true, method: 'email_password' }),
        ipAddress,
        userAgent
      ]);
    });

    res.json({
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        preferredLanguage: user.preferred_language
      },
      expiresIn: '24h',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Login failed'
    });
  }
});

/**
 * Logout endpoint
 * POST /api/auth/logout
 */
router.post('/logout', authenticateToken, logActivity('logout'), async (req, res) => {
  try {
    const token = req.token;
    const userId = req.user.userId;

    // Deactivate session
    await dbQuery(
      'UPDATE user_sessions SET is_active = false, logout_time = CURRENT_TIMESTAMP WHERE session_token = $1 AND user_id = $2',
      [token, userId]
    );

    res.json({
      message: 'Logout successful',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Logout failed'
    });
  }
});

/**
 * Verify token endpoint
 * GET /api/auth/verify
 */
router.get('/verify', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;

    // Get fresh user data
    const userResult = await dbQuery(
      'SELECT id, email, role, is_active, preferred_language FROM users WHERE id = $1',
      [userId]
    );

    if (userResult.rows.length === 0 || !userResult.rows[0].is_active) {
      return res.status(401).json({
        error: 'Invalid token',
        message: 'User not found or inactive'
      });
    }

    const user = userResult.rows[0];

    res.json({
      valid: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        preferredLanguage: user.preferred_language
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Token verification error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Token verification failed'
    });
  }
});

/**
 * Register endpoint (Admin only)
 * POST /api/auth/register
 */
router.post('/register', [
  authenticateToken,
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
  body('role').optional().isIn(['admin', 'user']).withMessage('Role must be admin or user')
], logActivity('user_create'), async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Admin access required'
      });
    }

    const { email, password, role = 'user' } = req.body;

    // Check if user already exists
    const existingUser = await dbQuery('SELECT id FROM users WHERE email = $1', [email]);
    if (existingUser.rows.length > 0) {
      return res.status(409).json({
        error: 'User exists',
        message: 'User with this email already exists'
      });
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create user
    const result = await dbQuery(`
      INSERT INTO users (email, password_hash, role)
      VALUES ($1, $2, $3)
      RETURNING id, email, role, created_at
    `, [email, passwordHash, role]);

    const newUser = result.rows[0];

    res.status(201).json({
      message: 'User created successfully',
      user: {
        id: newUser.id,
        email: newUser.email,
        role: newUser.role,
        createdAt: newUser.created_at
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Registration failed'
    });
  }
});

// Debug endpoint to check users
router.get('/debug/users', async (req, res) => {
  try {
    const result = await pool.query('SELECT id, email, role, is_active, created_at FROM users ORDER BY created_at DESC');
    res.json({
      count: result.rows.length,
      users: result.rows
    });
  } catch (error) {
    console.error('Debug users error:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
