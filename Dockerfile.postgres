FROM postgres:15-alpine

# Встановлюємо власника бази даних
ENV POSTGRES_DB=gads_db
ENV POSTGRES_USER=gads_user
ENV POSTGRES_PASSWORD=supersecurepassword123456

# Копіюємо повний дамп бази даних зі схемою та всіма даними
COPY init-db-complete-v3.sql /docker-entrypoint-initdb.d/01-init-db-complete-v3.sql

# Встановлюємо права доступу
RUN chmod 755 /docker-entrypoint-initdb.d/*.sql

# Відкриваємо порт
EXPOSE 5432

# Запускаємо PostgreSQL
CMD ["postgres"]