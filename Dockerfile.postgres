FROM postgres:15-alpine

# Встановлюємо власника бази даних
ENV POSTGRES_DB=gads_db
ENV POSTGRES_USER=gads_user
ENV POSTGRES_PASSWORD=supersecurepassword123456

# Копіюємо скрипти ініціалізації
COPY init-db-complete-v2.sql /docker-entrypoint-initdb.d/01-init-db-complete.sql
COPY content_export.sql /docker-entrypoint-initdb.d/02-content-export.sql
COPY database-fix.sql /docker-entrypoint-initdb.d/03-database-fix.sql

# Встановлюємо права доступу
RUN chmod 755 /docker-entrypoint-initdb.d/*.sql

# Відкриваємо порт
EXPOSE 5432

# Запускаємо PostgreSQL
CMD ["postgres"]