# gAds Supercharge - Deployment Context

## Deployment Architectures

### Current Frontend-Only Deployment
- **URL**: https://gads-supercharge.netlify.app/
- **Platform**: Netlify
- **Source**: Root directory (React app)
- **Build Command**: `npm run build` (Vite build)
- **Publish Directory**: `dist/`
- **Status**: Legacy deployment (CSV authentication, no database)
- **Limitations**: No user management, no real-time translations, static content only

### Full-Stack Deployment (Docker) - RECOMMENDED
- **Architecture**: Multi-container Docker setup
- **Services**: PostgreSQL + Node.js Backend + React Frontend + Redis
- **Orchestration**: Docker Compose
- **Reverse Proxy**: Nginx for production
- **Database**: PostgreSQL with persistent volumes and 487 content translations
- **Features**: Complete bilingual support, user authentication, session management
- **Status**: Production-ready with comprehensive localization

## Deployment Environments

### 1. Frontend-Only Production (Netlify)
**Configuration**:
- Automatic deployments from main branch
- Build command: `vite build`
- Node version: Latest LTS (18+)
- Environment variables: Set in Netlify dashboard
- Authentication: CSV-based (legacy)

### 2. Full-Stack Development (Docker)
**Configuration**:
- Local development with Docker Compose
- Hot reload for frontend and backend
- PostgreSQL with sample data
- JWT authentication with session tracking
- Real-time language switching
- MailHog email testing service

**Local URLs**:
- Frontend: http://localhost:5173
- Backend API: http://localhost:3001/api
- MailHog UI: http://localhost:8025
- PostgreSQL: localhost:5432
- Redis: localhost:6379

### 3. Full-Stack Production (Docker)
**Configuration**:
- Production-optimized Docker images
- SSL/TLS termination with Nginx
- Database backups and monitoring
- Health checks and logging
- Environment-based configuration

## Docker Deployment Guide

### Quick Start
```bash
# Clone repository
git clone <repository-url>
cd gads-supercharge

# Start all services
./start.sh

# Or manually
docker-compose up -d
```

### Service Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Frontend │    │  Node.js Backend │    │   PostgreSQL    │
│   (Port 5173)   │◄──►│   (Port 3001)   │◄──►│   (Port 5432)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────►│      Redis      │◄─────────────┘
                        │   (Port 6379)   │
                        └─────────────────┘
                                 │
                        ┌─────────────────┐
                        │     MailHog     │
                        │ SMTP: 1025      │
                        │ Web: 8025       │
                        └─────────────────┘
```

### Environment Configuration

#### Backend (.env)
```env
NODE_ENV=development
PORT=3001
DB_HOST=postgres
DB_PORT=5432
DB_NAME=gads_db
DB_USER=gads_user
DB_PASSWORD=gads_password
JWT_SECRET=your-super-secret-jwt-key-change-in-production
CORS_ORIGIN=http://localhost:5173
SMTP_HOST=mailhog
SMTP_PORT=1025
EMAIL_FROM=noreply@localhost
ADMIN_EMAIL=admin@localhost
```

#### Frontend (Vite)
```env
VITE_API_URL=http://localhost:3001/api
VITE_NODE_ENV=development
```

### Production Deployment
```bash
# Build production images
docker-compose -f docker-compose.yml -f docker-compose.prod.yml build

# Deploy with production settings
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Monitor services
docker-compose logs -f
```

### Health Monitoring
- **Database**: `pg_isready` health checks
- **Backend**: `/health` endpoint monitoring
- **Frontend**: Nginx status checks
- **Redis**: Connection monitoring

**Required Files for Frontend-Only Deployment**:
```
gads-services-mainpage/
├── src/                  # Application source code
├── public/               # Static assets including root.csv
├── package.json          # Dependencies and build scripts
├── vite.config.ts        # Build configuration
├── tailwind.config.js    # Styling configuration
├── tsconfig.json         # TypeScript configuration
├── netlify.toml          # Netlify deployment settings
└── index.html            # Entry point
```

### 2. Development Environment
**Local Setup**:
```bash
cd gads-services-mainpage
npm install
npm run dev
```

**Requirements**:
- Node.js 18+ 
- npm or yarn
- Modern browser with ES6+ support

## Build Process

### Frontend Build (`gads-services-mainpage/`)
1. **Install Dependencies**: `npm install`
2. **Type Checking**: TypeScript compilation
3. **Linting**: ESLint validation (optional)
4. **Build**: Vite bundles for production
5. **Output**: Static files in `dist/` directory

### Build Optimization
- **Tree Shaking**: Unused code elimination
- **Code Splitting**: Dynamic imports for route-based splitting
- **Asset Optimization**: Image and CSS optimization
- **Minification**: JavaScript and CSS minification

## Deployment Pipeline

### Netlify Configuration
```toml
# netlify.toml
[build]
  base = "gads-services-mainpage/"
  command = "npm run build"
  publish = "dist/"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
```

### Manual Deployment Steps
1. Navigate to project directory: `cd gads-services-mainpage`
2. Install dependencies: `npm install`
3. Build project: `npm run build`
4. Deploy `dist/` folder to hosting platform

## Authentication System

### CSV-based Authentication
**File**: `public/root.csv`
- Format: `login;password;role`
- Deployed with static assets
- Accessible at `/root.csv` endpoint

**Security Note**: This is a development/demo authentication system. For production, consider:
- Database-based user management
- Encrypted password storage
- JWT or session-based authentication
- OAuth integration

## Environment Management

### Development Environment
- Local development server with hot reload
- Source maps enabled for debugging
- Development-specific configurations

### Production Environment
- Optimized builds with minification
- Asset compression and caching
- Error tracking and monitoring

## Performance Optimization

### Build Optimizations
- **Bundle Analysis**: Monitor bundle size and dependencies
- **Lazy Loading**: Route-based code splitting implemented
- **Asset Optimization**: Automatic image and CSS optimization
- **CDN**: Netlify's global CDN for static assets

### Runtime Optimizations
- **Caching**: Browser caching for static assets
- **Compression**: Gzip/Brotli compression enabled
- **Preloading**: Critical resource preloading

## Monitoring and Maintenance

### Health Checks
- **Uptime Monitoring**: Netlify status dashboard
- **Performance**: Core Web Vitals tracking
- **Error Tracking**: Console error monitoring

### Maintenance Tasks
- **Dependency Updates**: Regular npm audit and updates
- **Security Patches**: Automated security updates
- **Performance Reviews**: Monthly performance audits

## Backup and Recovery

### Code Repository
- **Primary**: GitHub repository
- **Backup**: Automated GitHub backups
- **Versioning**: Git tags for releases

### Static Assets
- **Authentication Data**: `root.csv` backed up securely
- **Configuration**: All config files version controlled

## Scaling Considerations

### Traffic Scaling
- **CDN**: Netlify's global CDN handles traffic spikes
- **Caching**: Aggressive caching for static content
- **Performance**: Optimized bundle sizes

### Feature Scaling
- **Modular Architecture**: Easy to add new tools/features
- **Component Library**: Reusable UI components
- **Tool System**: Dynamic tool loading architecture

## Security Configuration

### HTTPS
- **SSL/TLS**: Automatic HTTPS via Netlify
- **HSTS**: HTTP Strict Transport Security headers
- **CSP**: Content Security Policy headers

### Authentication Security
- **Session Management**: localStorage-based sessions
- **Access Control**: Role-based access control
- **Input Validation**: Form validation and sanitization

## Troubleshooting

### Common Issues
1. **Build Failures**: Check Node.js version compatibility
2. **Authentication Issues**: Verify `root.csv` format and accessibility
3. **Routing Issues**: Ensure SPA redirect rules are configured
4. **Asset Loading**: Check public folder structure

### Debug Commands
```bash
# Local development
npm run dev

# Build locally
npm run build

# Preview production build
npm run preview

# Check dependencies
npm audit
```

## Deployment Checklist

### Pre-Deployment
- [ ] All tests passing
- [ ] Code linted and formatted
- [ ] Authentication data configured
- [ ] Performance audit completed
- [ ] Security headers configured

### Post-Deployment
- [ ] Site accessibility verified
- [ ] All routes working correctly
- [ ] Authentication flow tested
- [ ] Tool functionality verified
- [ ] Performance metrics reviewed

## Tool-Specific Deployment Notes

### Google Ads Tools
- Scripts generated are client-side only
- No server-side Google Ads API integration required
- All tools work with static deployment

### CSV Authentication
- `root.csv` must be in `public/` folder
- Accessible at `/root.csv` endpoint
- Format: `login;password;role`

### Asset Requirements
- All static assets in `public/` folder
- Images, icons, and data files
- Proper MIME type handling

## Production Deployment Options

### 1. Coolify v4 Deployment (RECOMMENDED)
- **Platform**: DigitalOcean VPS with Coolify v4.0.0-beta.420.5
- **Domain**: gads-supercharge.online (Cloudflare + wildcard DNS)
- **Architecture**: Full-stack Docker containers with automated SSL
- **Location**: `docker-compose.coolify.yml` в корені проекту (НЕ в `/coolify/` папці)
- **Features**: Auto-deploy, health monitoring, SSL certificates, backups
- **Status**: Production-ready configuration available

### ⚠️ ВАЖЛИВА ПРИМІТКА ПРО COOLIFY DEPLOYMENT
**Проблема**: Coolify копіює тільки файли з директорії где знаходиться `docker-compose.yml`. Якщо файл знаходиться в `/coolify/` папці, контейнери не мають доступу до `package.json`, `backend/` папки і інших файлів проекту.

**Рішення**: Використовуй `docker-compose.coolify.yml` з **кореня проекту**, а не з `/coolify/` папки. Цей файл має правильний контекст і доступ до всіх файлів проекту.

**Правильний процес деплою в Coolify**:
1. Скопіюй вміст `docker-compose.coolify.yml` (з кореня проекту)
2. Вставь в Coolify як docker-compose конфігурацію
3. Coolify скопіює всі файли проекту в контейнер
4. Контейнери зможуть знайти `package.json`, `backend/package.json` та інші файли

### 2. Manual Docker Deployment
- **Platform**: Any VPS or cloud provider
- **Configuration**: `docker-compose.yml` and `docker-compose.production.yml`
- **Features**: Multi-container setup with PostgreSQL, Redis, Nginx
- **Status**: Available for custom deployments

### 3. Legacy Netlify Deployment  
- **Platform**: Netlify (frontend-only)
- **URL**: https://gads-supercharge.netlify.app/
- **Limitations**: No database, CSV authentication, static content only
- **Status**: Deprecated but functional for demos

## Current Deployment Status (2025-07-15)

### Development Environment ✅
- **Full-stack Docker setup**: ✅ Working
- **PostgreSQL database**: ✅ Running with 487+ translations (v1.28-final-fix)
- **Backend API**: ✅ Running on port 3001 with FIXED authentication and admin routes
- **Frontend**: ✅ Running on port 5173
- **Authentication**: ✅ JWT-based with bcryptjs hashing - FIXED PASSWORD ISSUE
- **Security**: ✅ SQL injection prevention, XSS protection, input validation
- **Script Security**: ✅ All generators use escapeForScript and sanitizeJsonData
- **Email Service**: ✅ MailHog integration for development

### Production Deployment (Coolify)
- **Server**: DigitalOcean VPS ready
- **Domain**: gads-supercharge.online configured
- **SSL**: Cloudflare + Let's Encrypt
- **Services**: PostgreSQL + Redis + Backend + Frontend + Email
- **Configuration**: Complete Coolify deployment guide available
- **Monitoring**: Health checks, logging, automated backups
- **Security**: Production-grade security headers and configurations

### Deployment Architecture
```
gads-supercharge.online (Frontend - Port 80)
├── api.gads-supercharge.online (Backend API - Port 3001)
├── mail.gads-supercharge.online (Email UI - Port 8025)
└── PostgreSQL (Internal - Port 5432)
    ├── Redis (Internal - Port 6379)
    └── MailHog (Internal - Port 1025 SMTP)
```

### Email Service Configuration

**MailHog (Development/Testing)**
- **SMTP Server**: `mailhog:1025` (внутрішній)
- **Web Interface**: `mail.gads-supercharge.online:8025` (зовнішній)
- **Функціонал**: Перехоплює всі листи з форм і показує їх у веб-інтерфейсі
- **Використання**: Тестування форм зворотного зв'язку

**Форми які відправляють email**:
- Форма "Зв'язатися з нами" на головній сторінці
- Форма замовлення послуг
- Форма зворотного зв'язку з інструментів

**Налаштування в backend**:
```env
SMTP_HOST=mailhog
SMTP_PORT=1025
EMAIL_FROM=<EMAIL>
ADMIN_EMAIL=<EMAIL>
```

### Як переглядати отримані листи

**Крок 1: Відкрити веб-інтерфейс MailHog**
- URL: `https://mail.gads-supercharge.online:8025`
- Або: `http://mail.gads-supercharge.online:8025` (якщо SSL не налаштовано)

**Крок 2: Інтерфейс MailHog**
- **Головна сторінка**: Список всіх отриманих листів
- **Деталі листа**: Клік на лист для перегляду змісту
- **Відправник**: Показує з якої форми прийшов лист
- **Отримувач**: Завжди `<EMAIL>`
- **Тема**: Назва форми або інструменту
- **Текст**: Повний текст повідомлення з форми

**Крок 3: Функції MailHog**
- **Search**: Пошук по листах
- **Clear**: Очистити всі листи
- **Download**: Завантажити лист у форматі .eml
- **Source**: Переглянути raw формат листа
- **HTML/Plain**: Переключення між HTML і текстовим форматом

**Приклад використання:**
1. Користувач заповнює форму "Зв'язатися з нами"
2. Форма відправляється на backend
3. Backend надсилає лист через MailHog SMTP
4. Лист з'являється в веб-інтерфейсі `mail.gads-supercharge.online:8025`
5. Адміністратор читає лист і відповідає клієнту

**Примітка**: MailHog це тестовий email сервер. Для продакшну потрібно налаштувати реальний SMTP (SendGrid, Mailgun тощо).

### Deployment Status Summary v1.7 (2025-07-14)
1. ✅ **Coolify v4 Deployment Guide**: Complete documentation with proper file structure
2. ✅ **Docker Configurations**: Production-ready compose files with email services
3. ✅ **Security Hardening**: All 12 script generators secured and tested
4. ✅ **User Experience**: Fixed Preview/Generate button conflicts across all tools
5. ✅ **Translation System**: 509+ content keys with comprehensive localization
6. ✅ **Email Integration**: MailHog (dev) + Postfix (prod) configurations
7. ✅ **Back Navigation**: Unified "Back to Generator" functionality
8. ✅ **File Structure Fix**: `docker-compose.coolify.yml` в корені проекту для правильного доступу до файлів
9. ✅ **Deploy to Production**: Successfully deployed to gads-supercharge.online with working authentication
10. ✅ **Automatic User Creation**: Fixed backend container to create users automatically on startup
11. ✅ **Working Login System**: Users can <NAME_EMAIL> / Admin2025!Secure#
12. 🔄 **Performance Testing**: Load testing and optimization
13. 🔄 **Monitoring Setup**: Alerts and log aggregation

### Deployment Status Summary v1.28-final-fix (2025-07-15)
1. ✅ **CRITICAL PASSWORD FIX**: Fixed bcrypt password hashing issue that prevented login
2. ✅ **Docker Rebuild**: Complete rebuild of all Docker images with proper file structure
3. ✅ **Backend Dockerfile Fix**: Fixed package.json copying issue in backend container
4. ✅ **Debug Endpoint**: Added /api/auth/debug/fix-passwords for production password fix
5. ✅ **Multi-platform Images**: All images built for AMD64 + ARM64 architectures
6. ✅ **Production Ready**: All containers tested and working with correct authentication
7. ✅ **Database Schema**: Complete PostgreSQL schema with 487+ translations
8. ✅ **User Management**: Full authentication system with role-based access - WORKING
9. ✅ **Translation System**: Comprehensive bilingual support (EN/UA)
10. ✅ **Security Features**: SQL injection prevention, XSS protection, input validation
11. ✅ **Script Generators**: All 12 tools with security hardening and obfuscation
12. ✅ **Admin Dashboard**: Complete admin interface with user management - WORKING
13. ✅ **Email Integration**: Contact form with MailHog testing setup - WORKING
14. ✅ **Docker Configuration**: Multi-service production-ready setup
15. ✅ **API Documentation**: Complete backend API with proper error handling
16. ✅ **Frontend Optimization**: React app with Vite build system
17. ✅ **Database Initialization**: Automated schema setup with init-db-complete-v3.sql
18. 🚀 **Production Deployment**: Ready for immediate Coolify v4 deployment
19. 🔄 **Performance Monitoring**: Load testing and optimization pending

### Previous Deployment Status Summary v1.27 (2025-07-15)
1. ✅ **Database Schema Update**: Complete PostgreSQL schema with 487+ translations
2. ✅ **User Management**: Full authentication system with role-based access - FIXED
3. ✅ **Translation System**: Comprehensive bilingual support (EN/UA)
4. ✅ **Security Features**: SQL injection prevention, XSS protection, input validation
5. ✅ **Script Generators**: All 12 tools with security hardening and obfuscation
6. ✅ **Admin Dashboard**: Complete admin interface with user management - FIXED
7. ✅ **Email Integration**: Contact form with MailHog testing setup - WORKING
8. ✅ **Docker Configuration**: Multi-service production-ready setup
9. ✅ **API Documentation**: Complete backend API with proper error handling
10. ✅ **Frontend Optimization**: React app with Vite build system
11. ✅ **Database Initialization**: Automated schema setup with init-db-complete-v3.sql
12. ✅ **Bug Fixes**: Fixed req.user.id vs req.user.userId authentication inconsistency
13. ✅ **Admin Routes**: Fixed tracking code, SEO settings, content management APIs
14. ❌ **Production Deployment**: Password authentication issue prevented login
15. 🔄 **Performance Monitoring**: Load testing and optimization pending

### КРИТИЧНЕ ВИПРАВЛЕННЯ v1.28-final-fix: Password Authentication

**Проблема**: Backend повертав 500 помилку при логіні через неправильні bcrypt hash паролів в базі даних.

**Симптоми**:
- `POST /api/auth/login` повертає 500 Internal Server Error
- В логах backend: "Login failed"
- Frontend показує "❌ Login failed: null"
- Користувачі не можуть увійти в систему

**Причина**: В production базі даних зберігалися неправильні bcrypt hash паролів, які не співпадали з реальними паролями користувачів.

**Рішення v1.28-final-fix**:
1. **Додано debug endpoint** `/api/auth/debug/fix-passwords` для автоматичного виправлення паролів
2. **Пересобрані Docker образи** з виправленнями
3. **Виправлено backend Dockerfile** для правильного копіювання файлів

**Кроки для виправлення в production**:
1. Деплой нових образів `v1.28-final-fix` в Coolify
2. Викликати endpoint для виправлення паролів:
   ```bash
   curl -X POST https://api.gads-supercharge.online/api/auth/debug/fix-passwords
   ```
3. Протестувати логін з правильними паролями:
   - Email: `<EMAIL>`
   - Password: `Admin2025!Secure#`

**Правильні паролі після виправлення**:
- `<EMAIL>` / `Admin2025!Secure#`
- `<EMAIL>` / `User2025!Strong#`
- `<EMAIL>` / `Demo2025!Test#`
- `<EMAIL>` / `Test2025!Complex#`

### Coolify Deployment Troubleshooting

**Проблема 1**: `npm error enoent Could not read package.json: Error: ENOENT: no such file or directory`

**Причина**: Coolify копіює тільки файли з директорії где знаходиться docker-compose.yml файл. Якщо файл в `/coolify/` папці, контейнери не мають доступу до кореневих файлів проекту.

**Рішення**: 
1. Використовуй `docker-compose.coolify.yml` з **кореня проекту**
2. Переконайся що всі path в docker-compose відносяться до кореня проекту
3. Backend контейнер повинен мати доступ до `backend/package.json`
4. Frontend контейнер повинен мати доступ до `package.json` в корені

**Проблема 2**: `no matching manifest for linux/amd64 in the manifest list entries`

**Причина**: Docker образи зібрані на Apple Silicon (ARM64), а Coolify запускає на AMD64 (x86_64) архітектурі.

**Рішення**: 
1. **Multi-platform build (РЕКОМЕНДОВАНО)**:
   ```bash
   # Backend
   docker buildx build --platform linux/amd64,linux/arm64 -t aprokudan/gads-supercharge-backend:v1.9 -f backend/Dockerfile --push .
   
   # Frontend  
   docker buildx build --platform linux/amd64,linux/arm64 -t aprokudan/gads-supercharge-frontend:v1.9 -f Dockerfile.frontend --push .
   
   # PostgreSQL
   docker buildx build --platform linux/amd64,linux/arm64 -t aprokudan/gads-supercharge-postgres:v1.9 -f Dockerfile.postgres --push .
   ```

2. **AMD64-only build (швидше)**:
   ```bash
   # Backend
   docker buildx build --platform linux/amd64 -t aprokudan/gads-supercharge-backend:v1.9 -f backend/Dockerfile --push .
   
   # Frontend
   docker buildx build --platform linux/amd64 -t aprokudan/gads-supercharge-frontend:v1.9 -f Dockerfile.frontend --push .
   
   # PostgreSQL
   docker buildx build --platform linux/amd64 -t aprokudan/gads-supercharge-postgres:v1.9 -f Dockerfile.postgres --push .
   ```

3. **Перевірка manifest після build**:
   ```bash
   docker manifest inspect aprokudan/gads-supercharge-backend:v1.9
   ```

**ВАЖЛИВО**: Обов'язково оновлюй версії в `docker-compose.coolify.yml` після пересборки образів і використовуй той самий тег що був зібраний з правильною архітектурою.

**Проблема 3**: `nginx: [emerg] host not found in upstream "backend"`

**Причина**: Nginx не може знайти backend upstream при старті контейнера.

**Рішення**: Виправлено nginx конфігурацію - API запити тепер редиректяться на `https://api.gads-supercharge.online`.

**Проблема 4**: `no available server` / 503 помилка

**Причина**: Coolify проксі не може з'єднатись з контейнером через неправильні labels або health checks.

**Рішення**: Додано правильні Coolify labels і health checks для всіх сервісів.

**Правильна структура файлів для Coolify**:
```
gads-supercharge/
├── docker-compose.coolify.yml  ← Використовуй цей файл в Coolify
├── package.json                ← Frontend package.json
├── backend/
│   └── package.json           ← Backend package.json
├── Dockerfile.frontend.simple  ← Frontend Dockerfile
├── nginx.conf                  ← Nginx конфігурація
└── coolify/
    └── docker-compose.production.yml  ← НЕ використовуй цей файл
```

**Docker Hub Images v1.28-final-fix**:
- `aprokudan/gads-supercharge-backend:v1.28-final-fix` - Backend Node.js application with FIXED password authentication
- `aprokudan/gads-supercharge-frontend:v1.28-final-fix` - Frontend React application with CORRECT API URL
- `aprokudan/gads-supercharge-postgres:v1.27` - PostgreSQL with complete schema (487+ translations) and users
- **Статус**: ✅ Готові до продакшн з multi-platform підтримкою (AMD64 + ARM64)
- **Виправлення**: ✅ Fixed bcrypt password hashing, debug endpoint for password fix, Dockerfile issues

**Previous Docker Hub Images v1.27**:
- `aprokudan/gads-supercharge-backend:v1.27` - Backend Node.js application with FIXED authentication
- `aprokudan/gads-supercharge-frontend:v1.27` - Frontend React application with CORRECT API URL
- **Виправлення**: ✅ Fixed req.user.id vs req.user.userId, admin routes, email system, API connectivity

## Покрокова інструкція Coolify деплою

### Крок 1: Підготовка Docker образів

**1.1 Збілдити та запушити backend образ (v1.28-final-fix):**
```bash
cd gads-supercharge
docker buildx build --platform linux/amd64,linux/arm64 -t aprokudan/gads-supercharge-backend:v1.28-final-fix -f backend/Dockerfile --push .
```

**1.2 Збілдити та запушити frontend образ (v1.28-final-fix):**
```bash
docker buildx build --platform linux/amd64,linux/arm64 -t aprokudan/gads-supercharge-frontend:v1.28-final-fix -f Dockerfile.frontend --push .
```

**1.3 Збілдити та запушити PostgreSQL образ (v1.27 - без змін):**
```bash
docker buildx build --platform linux/amd64,linux/arm64 -t aprokudan/gads-supercharge-postgres:v1.27 -f Dockerfile.postgres --push .
```

**ВАЖЛИВО**: Після збирання образів **ОБОВ'ЯЗКОВО** оновити версії в `docker-compose.coolify.yml`:
- Backend: `aprokudan/gads-supercharge-backend:v1.28-final-fix`
- Frontend: `aprokudan/gads-supercharge-frontend:v1.28-final-fix`
- PostgreSQL: `aprokudan/gads-supercharge-postgres:v1.27` (без змін)

### Крок 2: Coolify конфігурація

**2.1 Створити новий проект в Coolify:**
- Увійти в Coolify панель
- Створити новий проект
- Вибрати "Docker Compose"

**2.2 Скопіювати docker-compose.coolify.yml:**
- Відкрити файл `/docker-compose.coolify.yml` з кореня проекту
- Скопіювати весь вміст
- Вставити в Coolify

**2.3 Налаштувати домени:**
- **Frontend**: `gads-supercharge.online`
- **Backend**: `api.gads-supercharge.online`
- **MailHog**: `mail.gads-supercharge.online`

### Крок 3: Деплой та верифікація

**3.1 Запустити деплой:**
- Натиснути "Deploy" в Coolify
- Чекати поки всі контейнери запустяться

**3.2 Перевірити статус сервісів:**
- Всі сервіси повинні бути "running (healthy)"
- Перевірити логи кожного сервісу

**3.3 Тестувати додаток:**
- Відкрити `https://gads-supercharge.online`
- Перевірити що сайт завантажується
- Протестувати API: `https://api.gads-supercharge.online/health`
- Перевірити email: `https://mail.gads-supercharge.online:8025`

### Крок 4: Налаштування доменів в Coolify

**4.1 Додати домени в сервіси:**
- Зайти в налаштування Frontend сервісу
- Додати домен: `gads-supercharge.online`
- Увімкнути HTTPS
- Повторити для Backend та MailHog

**4.2 Перевірити SSL сертифікати:**
- Coolify автоматично генерує Let's Encrypt сертифікати
- Перевірити що всі домени мають валідні SSL

### Крок 5: Моніторинг та налагодження

**5.1 Перевірити health checks:**
- Всі сервіси повинні проходити health checks
- Якщо щось не працює - дивитися логи

**5.2 Типові проблеми:**
- **503 помилка**: Перезапустити проект або створити новий
- **DNS проблеми**: Перевірити налаштування доменів
- **Контейнер не стартує**: Перевірити образи в Docker Hub

### Production Readiness Checklist v1.7
- ✅ **Security**: SQL injection prevention, XSS protection, input validation
- ✅ **User Interface**: All tools tested and user flows optimized
- ✅ **Internationalization**: Complete Ukrainian/English translations
- ✅ **Docker Configuration**: Multi-service setup with health checks
- ✅ **Documentation**: Complete deployment guides and API documentation
- ✅ **Email Services**: Contact form handling with production SMTP
- ✅ **SSL Configuration**: Cloudflare + Let's Encrypt working
- ✅ **Domain Setup**: gads-supercharge.online configured and working
- ✅ **Production Testing**: Successfully deployed and tested on production environment
- ✅ **Authentication System**: Working login with automatic user creation
- ✅ **Backend API**: Health checks passing at https://api.gads-supercharge.online/health
- ✅ **Frontend**: Fully functional at https://gads-supercharge.online

## Development Files Organization

### misc/ Directory
The `misc/` directory contains development utilities and test files that should **NOT** be deployed to production:

**Database Scripts:**
- `*.cjs` - User management and database setup scripts
- `*.sql` - SQL migration and update scripts
- `*.json` - Translation files and test data

**Test Files:**
- `*.html` - Test pages and API testing interfaces
- `test-*.cjs` - Authentication and API testing scripts

**Note:** These files were moved from the project root to keep the main directory clean and organized. They are for development use only and should be excluded from production builds.
