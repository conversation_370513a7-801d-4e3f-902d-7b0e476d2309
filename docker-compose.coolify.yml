version: '3.8'

# ===============================================================================
# GADS SUPERCHARGE - PRODUCTION DEPLOYMENT v1.28-final-fix
# ===============================================================================
# 🎯 GOOGLE ADS AUTOMATION PLATFORM
# ✅ DOMAINS: gads-supercharge.online, api.gads-supercharge.online, mail.gads-supercharge.online
# ✅ FEATURES: 12+ Google Ads script generators, bilingual support (UK/EN), 487+ translations
# ✅ SERVICES: PostgreSQL, Redis, Backend API, Frontend React, MailHog Email
# 🔑 ADMIN LOGIN: <EMAIL> / Admin2025!Secure#
# 📧 EMAIL TEST: https://mail.gads-supercharge.online:8025
#
# 🚨 КРИТИЧНЕ ВИПРАВЛЕННЯ v1.28-final-fix:
# - ВИПРАВЛЕНО: Проблему з автентифікацією паролів (bcrypt hash)
# - ОНОВЛЕНО: Backend образ з debug endpoint для виправлення паролів
# - ВИПРАВЛЕНО: Backend Dockerfile для правильного копіювання файлів
#
# 📋 ПІСЛЯ ДЕПЛОЮ ВИКОНАТИ:
# curl -X POST https://api.gads-supercharge.online/api/auth/debug/fix-passwords
# ===============================================================================

networks:
  default:
    external: true
    name: coolify

volumes:
  postgres_data:
  redis_data:
  mailhog_data:

services:
  postgres:
    image: aprokudan/gads-supercharge-postgres:v1.27
    platform: linux/amd64
    restart: unless-stopped
    environment:
      POSTGRES_DB: gads_db
      POSTGRES_USER: gads_user
      POSTGRES_PASSWORD: supersecurepassword123456
      POSTGRES_HOST_AUTH_METHOD: trust
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U gads_user -d gads_db"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    labels:
      - "coolify.managed=true"

  redis:
    image: redis:7-alpine
    platform: linux/amd64
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "coolify.managed=true"

  backend:
    # 🔧 BACKEND API - Node.js + Express + PostgreSQL
    # 🚨 v1.28-final-fix: ВИПРАВЛЕНО проблему з автентифікацією паролів
    # 📋 Включає debug endpoint: /api/auth/debug/fix-passwords
    # Option 1: Build from source (slower, always latest code)
    # build:
    #   context: .
    #   dockerfile: backend/Dockerfile
    # Option 2: Use pre-built image (faster, stable)
    image: aprokudan/gads-supercharge-backend:v1.28-final-fix
    platform: linux/amd64
    restart: unless-stopped
    expose:
      - "3001"
    environment:
      NODE_ENV: production
      PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: gads_db
      DB_USER: gads_user
      DB_PASSWORD: supersecurepassword123456
      JWT_SECRET: supersecurejwtsecret1234567890abcdef
      REDIS_HOST: redis
      REDIS_PORT: 6379
      CORS_ORIGIN: https://gads-supercharge.online,https://www.gads-supercharge.online
      SMTP_HOST: mailhog
      SMTP_PORT: 1025
      EMAIL_FROM: <EMAIL>
      ADMIN_EMAIL: <EMAIL>
      DOMAIN: gads-supercharge.online
      FRONTEND_URL: https://gads-supercharge.online
      API_URL: https://api.gads-supercharge.online
    depends_on:
      - postgres
      - redis
      - mailhog
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "coolify.managed=true"
      - "coolify.url=api.gads-supercharge.online"
      - "coolify.port=3001"
      - "coolify.https=true"
      - "coolify.healthcheck.enabled=true"
      - "coolify.healthcheck.path=/health"

  frontend:
    # Option 1: Build from source (slower, always latest code)
    # build:
    #   context: .
    #   dockerfile: Dockerfile.frontend
    #   args:
    #     - VITE_API_URL=https://api.gads-supercharge.online/api
    # Option 2: Use pre-built image (faster, stable)  
    image: aprokudan/gads-supercharge-frontend:v1.28-final-fix
    platform: linux/amd64
    restart: unless-stopped
    expose:
      - "80"
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    labels:
      - "coolify.managed=true"
      - "coolify.url=gads-supercharge.online"
      - "coolify.port=80"
      - "coolify.https=true"
      - "coolify.healthcheck.enabled=true"
      - "coolify.healthcheck.path=/health"

  mailhog:
    image: mailhog/mailhog:latest
    platform: linux/amd64
    restart: unless-stopped
    expose:
      - "8025"
    environment:
      MH_STORAGE: maildir
      MH_MAILDIR_PATH: /tmp
    volumes:
      - mailhog_data:/tmp
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8025"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "coolify.managed=true"
      - "coolify.url=mail.gads-supercharge.online"
      - "coolify.port=8025"
      - "coolify.https=true"
      - "coolify.healthcheck.enabled=true"
      - "coolify.healthcheck.path=/health"