worker_processes auto;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    sendfile on;
    keepalive_timeout 65;

    # Define upstream for backend service
    upstream backend_service {
        server backend:3001;
    }

    # Define upstream for frontend service
    # Note: The frontend service in docker-compose.yml uses Vite dev server on 5173
    # For a production build, this should typically point to a container serving static files on port 80.
    # We will assume the 'frontend' service from docker-compose.yml is what's being run.
    upstream frontend_service {
        server frontend:5173;
    }

    server {
        listen 80;
        server_name gads-supercharge.online www.gads-supercharge.online;

        # Log files
        access_log /var/log/nginx/access.log;
        error_log /var/log/nginx/error.log;

        # Proxy API requests to the backend service
        location /api/ {
            proxy_pass http://backend_service/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # Proxy all other requests to the frontend service
        location / {
            proxy_pass http://frontend_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade"; # Required for WebSocket connections used by Vite HMR
        }
    }
}
