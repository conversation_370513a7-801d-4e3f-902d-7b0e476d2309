-- Fix production passwords with correct hashes
-- Generated on 2025-07-15

-- Update admin password (Admin2025!Secure#)
UPDATE users SET password_hash = '$2a$12$H1yzeEnTKTfIFHNjpMdzKug.M6epnrSOr5g9/wB9rybbJq9fyu/oy' WHERE email = '<EMAIL>';

-- Update user password (User2025!Strong#)
UPDATE users SET password_hash = '$2a$12$nRCtrKdhZCU6t7UmpucRXusZpvsthfnbFAjYAyMigsaxEowJwSx9u' WHERE email = '<EMAIL>';

-- Update demo password (Demo2025!Test#)
UPDATE users SET password_hash = '$2a$12$HFL1UJlKoBbMPXrtcZabwuEoEDYfP8fcTIUvFDxgWRU5uw7nuyInm' WHERE email = '<EMAIL>';

-- Update test password (Test2025!Complex#)
UPDATE users SET password_hash = '$2a$12$2uUPsdThcsQ7vCmUHU1WNeLOfnuCNQmPVeSapbrDykDLmuWmiEjKy' WHERE email = '<EMAIL>';

-- Verify updates
SELECT email, length(password_hash) as hash_length, 
       CASE WHEN password_hash = '$2a$12$hSTkxvDXTx.0TLBmuN7JMeYT3S6ZJVL1QFU6G7kTpbvVhAHshwH8G' 
            THEN 'CORRECT' 
            ELSE 'INCORRECT' 
       END as hash_status
FROM users 
WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>')
ORDER BY email;
